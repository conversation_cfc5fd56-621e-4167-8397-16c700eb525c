import 'package:build_mate/presentation/routes/route_constants.dart';
import 'package:build_mate/presentation/view_models/user/home_tab_view_model.dart';
import 'package:build_mate/theme/theme_providers/custom_colors_provider.dart';
import 'package:build_mate/theme/theme_providers/theme_mode_provider.dart';
import 'package:flutter/material.dart';
import 'package:build_mate/theme/colors.dart';
import 'package:build_mate/theme/font/typography.dart';
import 'package:go_router/go_router.dart';
import 'package:build_mate/presentation/components/button/primary_button.dart';
import 'package:build_mate/presentation/components/widgets/service_categories_section.dart';
import 'package:build_mate/presentation/components/widgets/popular_services_section.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class HomeTabView extends ConsumerWidget {
  const HomeTabView({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final state = ref.watch(homeTabViewModelProvider);
    final customColors = ref.watch(customColorsProvider);
    final themeMode = ref.watch(themeModeProvider);

    // Use dark blue for light theme, custom surface variant for dark theme
    final appBarColor =
        themeMode == ThemeMode.light
            ? darkBlueColor
            : customColors.surfaceVariant;

    // Text and icon colors that work well with the background
    final textColor =
        themeMode == ThemeMode.light
            ? Colors.white
            : customColors.textPrimaryColor;
    final subtitleColor =
        themeMode == ThemeMode.light
            ? Colors.white70
            : customColors.textPrimaryColor.withValues(alpha: 0.7);

    return Scaffold(
      backgroundColor:
          themeMode == ThemeMode.light
              ? Colors.white
              : const Color(0xFF1A1A1A), // Dar
      body: Column(
        children: [
          Container(
            color: appBarColor,
            child: SafeArea(
              bottom: false,
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Row(
                      children: [
                        // Show loading state while image is loading
                        SizedBox(
                          width: 40,
                          height: 40,
                          child: ClipOval(
                            child: Image.network(
                              state.profileUrl,
                              fit: BoxFit.cover,
                              loadingBuilder: (
                                context,
                                child,
                                loadingProgress,
                              ) {
                                if (loadingProgress == null) return child;
                                return Container(
                                  width: 40,
                                  height: 40,
                                  alignment: Alignment.center,
                                  child: SizedBox(
                                    width: 24,
                                    height: 24,
                                    child: CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        textColor,
                                      ),
                                    ),
                                  ),
                                );
                              },
                              errorBuilder: (context, error, stackTrace) {
                                return CircleAvatar(
                                  radius: 20,
                                  backgroundColor: subtitleColor.withValues(
                                    alpha: 0.3,
                                  ),
                                  child: Icon(
                                    Icons.person,
                                    color: subtitleColor,
                                  ),
                                );
                              },
                            ),
                          ),
                        ),
                        const SizedBox(width: 12),
                        Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              'Hi ${state.username}',
                              style: MyTypography.SemiBold.copyWith(
                                fontSize: 16,
                                color: textColor,
                              ),
                            ),
                            Row(
                              children: [
                                Icon(
                                  Icons.location_on,
                                  size: 16,
                                  color: subtitleColor,
                                ),
                                Text(
                                  state.currentLocation,
                                  style: MyTypography.Medium.copyWith(
                                    fontSize: 14,
                                    color: subtitleColor,
                                  ),
                                ),
                              ],
                            ),
                          ],
                        ),
                      ],
                    ),
                    IconButton(
                      icon: Icon(Icons.notifications_none, color: textColor),
                      onPressed: () {},
                    ),
                  ],
                ),
              ),
            ),
          ),
          Expanded(
            child: SingleChildScrollView(
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildSearchBar(context),
                    const SizedBox(height: 24),
                    // Use the updated ServiceCategoriesSection without passing categories
                    const ServiceCategoriesSection(),
                    // const SizedBox(height: 24),
                    // _buildPromoBanner(),
                    const SizedBox(height: 16),
                    const PopularServicesSection(), // Use the new component
                    const SizedBox(height: 24),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchBar(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'What service do you want?',
          style: MyTypography.SemiBold.copyWith(fontSize: 18),
        ),
        const SizedBox(height: 12),
        PrimaryButton(
          text: 'Post your job',
          onPressed: () {
            context.pushNamed(
              RouteConstants.POST_JOB_SCREEN,
            ); // Adjust route as needed
          },
        ),
      ],
    );
  }
}
